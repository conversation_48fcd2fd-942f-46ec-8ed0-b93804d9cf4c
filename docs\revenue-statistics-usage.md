# 收入数据分析功能使用说明

## 功能概述

收入数据分析功能提供了全面的收入统计和分析能力，支持以下维度的统计：

### 1. 时间维度统计
- **概览统计**：不限时间范围的总体收入概览
- **趋势分析**：按日、周、月的收入趋势变化

### 2. 服务维度统计
- **服务类型统计**：按服务类型分组的收入统计
- **具体服务统计**：按具体服务项目的收入统计

### 3. 员工维度统计
- **员工收入排行**：按员工统计的收入数据
- **员工业绩分析**：包含主订单和追加服务的综合统计

### 4. 收入构成分析
- **主订单 vs 追加服务**：收入来源构成分析
- **优惠构成分析**：权益卡、代金券优惠分析
- **订单数量构成**：不同类型订单的数量占比

## 数据统计规则

### 有效订单定义
只统计状态为"已完成"和"已评价"的订单，确保数据的准确性和一致性。

### 收入计算方式
- **原价**：订单的原始价格（未扣除优惠前）
- **实付金额**：用户实际支付的金额（totalFee）
- **优惠金额**：权益卡抵扣 + 代金券抵扣
- **净收入**：实付金额（即最终收入）

### 时间范围处理
- 不传时间参数：统计全部历史数据
- 传入时间范围：统计指定时间段内的数据
- 时间格式：YYYY-MM-DD

## API 使用示例

### 1. 获取收入概览
```bash
# 获取全部历史收入概览
GET /revenue-statistics/overview

# 获取指定时间段收入概览
GET /revenue-statistics/overview?startDate=2024-01-01&endDate=2024-01-31
```

### 2. 获取收入趋势
```bash
# 获取最近30天按日统计的收入趋势
GET /revenue-statistics/trend?startDate=2024-01-01&endDate=2024-01-31&periodType=day

# 获取最近12周按周统计的收入趋势
GET /revenue-statistics/trend?startDate=2024-01-01&endDate=2024-03-31&periodType=week

# 获取最近12个月按月统计的收入趋势
GET /revenue-statistics/trend?startDate=2023-01-01&endDate=2023-12-31&periodType=month
```

### 3. 获取服务收入统计
```bash
# 获取所有服务的收入统计（按收入降序）
GET /revenue-statistics/service?sortBy=totalRevenue&sortOrder=desc

# 获取指定服务类型的收入统计
GET /revenue-statistics/service?serviceTypeId=1

# 获取指定服务的收入统计
GET /revenue-statistics/service?serviceId=1

# 分页获取服务收入统计
GET /revenue-statistics/service?page=1&pageSize=10
```

### 4. 获取员工收入统计
```bash
# 获取员工收入排行榜（按总收入降序）
GET /revenue-statistics/employee?sortBy=totalRevenue&sortOrder=desc

# 获取指定员工的收入统计
GET /revenue-statistics/employee?employeeId=1

# 获取指定时间段的员工收入统计
GET /revenue-statistics/employee?startDate=2024-01-01&endDate=2024-01-31
```

### 5. 获取收入构成分析
```bash
# 获取收入构成分析
GET /revenue-statistics/composition

# 获取指定时间段的收入构成分析
GET /revenue-statistics/composition?startDate=2024-01-01&endDate=2024-01-31
```

## 数据字段说明

### 收入概览数据结构
```json
{
  "totalRevenue": 15000.00,           // 总收入
  "totalOriginalPrice": 18000.00,     // 总原价
  "totalDiscount": 3000.00,           // 总优惠金额
  "discountRate": "16.67",            // 优惠率（%）
  "mainOrder": {                      // 主订单数据
    "orderCount": 50,                 // 订单数量
    "totalRevenue": 12000.00,         // 总收入
    "totalOriginalPrice": 14000.00,   // 总原价
    "totalDiscount": 2000.00,         // 总优惠
    "avgOrderValue": 240.00           // 平均订单价值
  },
  "additionalService": {              // 追加服务数据
    "orderCount": 20,                 // 订单数量
    "totalRevenue": 3000.00,          // 总收入
    "totalOriginalPrice": 4000.00,    // 总原价
    "totalDiscount": 1000.00,         // 总优惠
    "avgOrderValue": 150.00           // 平均订单价值
  },
  "totalOrderCount": 70,              // 总订单数
  "avgOrderValue": "214.29"           // 平均订单价值
}
```

### 趋势数据结构
```json
[
  {
    "period": "2024-01-01",           // 时间周期
    "mainOrder": {                    // 主订单数据
      "orderCount": 10,
      "totalRevenue": 2400.00,
      "totalOriginalPrice": 2800.00,
      "totalDiscount": 400.00
    },
    "additionalService": {            // 追加服务数据
      "orderCount": 5,
      "totalRevenue": 750.00,
      "totalOriginalPrice": 1000.00,
      "totalDiscount": 250.00
    },
    "totalRevenue": 3150.00,          // 当期总收入
    "totalOriginalPrice": 3800.00,    // 当期总原价
    "totalDiscount": 650.00,          // 当期总优惠
    "totalOrderCount": 15             // 当期总订单数
  }
]
```

## 注意事项

1. **性能考虑**：大时间范围的统计查询可能较慢，建议合理设置时间范围
2. **数据一致性**：统计数据基于订单的最终状态，已取消或退款的订单不计入统计
3. **时区处理**：所有时间统计基于服务器时区
4. **分页限制**：列表接口支持分页，建议设置合理的pageSize避免一次性加载过多数据

## 业务应用场景

### 1. 日常运营分析
- 查看每日/每周/每月的收入趋势
- 分析收入构成，了解主订单和追加服务的贡献比例
- 监控优惠活动对收入的影响

### 2. 服务项目分析
- 识别高收入服务项目
- 分析服务项目的订单量和平均价值
- 优化服务定价策略

### 3. 员工绩效评估
- 员工收入排行榜
- 员工业绩趋势分析
- 制定员工激励政策

### 4. 业务决策支持
- 基于历史数据预测未来收入
- 分析季节性收入变化
- 制定营销策略和促销计划
