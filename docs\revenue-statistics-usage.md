# 收入数据分析功能使用说明

## 功能概述

收入数据分析功能提供了全面的收入统计和分析能力，支持以下维度的统计：

### 1. 时间维度统计
- **概览统计**：不限时间范围的总体收入概览
- **趋势分析**：按日、周、月的收入趋势变化

### 2. 服务维度统计
- **服务类型统计**：按服务类型分组的收入统计
- **具体服务统计**：按具体服务项目的收入统计

### 3. 员工维度统计
- **员工收入排行**：按员工统计的收入数据
- **员工业绩分析**：包含主订单和追加服务的综合统计

### 4. 收入构成分析
- **主订单 vs 追加服务**：收入来源构成分析
- **优惠构成分析**：权益卡、代金券优惠分析
- **订单数量构成**：不同类型订单的数量占比

## 数据统计规则

### 统计范围定义
统计包含以下状态的订单：**已完成、已评价、退款中、已退款**，确保收入数据的完整性。

### 收入计算方式
- **原价**：订单的原始价格（未扣除任何优惠前）
- **实付金额**：用户实际支付的金额（包含所有状态订单）
- **有效收入**：已完成和已评价订单的收入（真正确认的收入）
- **退款金额**：退款中和已退款订单的金额
- **净收入**：有效收入 - 退款金额（最终实际收入）
- **优惠金额**：权益卡抵扣 + 代金券抵扣
- **优惠率**：优惠金额占原价的百分比

### 时间范围处理
- 不传时间参数：统计全部历史数据
- 传入时间范围：统计指定时间段内的数据
- 时间格式：YYYY-MM-DD

## API 使用示例

### 1. 获取收入概览
```bash
# 获取全部历史收入概览
GET /revenue-statistics/overview

# 获取指定时间段收入概览
GET /revenue-statistics/overview?startDate=2024-01-01&endDate=2024-01-31
```

### 2. 获取收入趋势
```bash
# 获取最近30天按日统计的收入趋势
GET /revenue-statistics/trend?startDate=2024-01-01&endDate=2024-01-31&periodType=day

# 获取最近12周按周统计的收入趋势
GET /revenue-statistics/trend?startDate=2024-01-01&endDate=2024-03-31&periodType=week

# 获取最近12个月按月统计的收入趋势
GET /revenue-statistics/trend?startDate=2023-01-01&endDate=2023-12-31&periodType=month
```

### 3. 获取服务收入统计
```bash
# 获取所有服务的收入统计（按收入降序）
GET /revenue-statistics/service?sortBy=totalRevenue&sortOrder=desc

# 获取指定服务类型的收入统计
GET /revenue-statistics/service?serviceTypeId=1

# 获取指定服务的收入统计
GET /revenue-statistics/service?serviceId=1

# 分页获取服务收入统计
GET /revenue-statistics/service?page=1&pageSize=10
```

### 4. 获取员工收入统计
```bash
# 获取员工收入排行榜（按总收入降序）
GET /revenue-statistics/employee?sortBy=totalRevenue&sortOrder=desc

# 获取指定员工的收入统计
GET /revenue-statistics/employee?employeeId=1

# 获取指定时间段的员工收入统计
GET /revenue-statistics/employee?startDate=2024-01-01&endDate=2024-01-31
```

### 5. 获取收入构成分析
```bash
# 获取收入构成分析
GET /revenue-statistics/composition

# 获取指定时间段的收入构成分析
GET /revenue-statistics/composition?startDate=2024-01-01&endDate=2024-01-31
```

## 数据字段说明

### 收入概览数据结构
```json
{
  "totalRevenue": 15000.00,           // 有效收入总额（已完成+已评价）
  "totalOriginalPrice": 18000.00,     // 有效订单原价总额
  "totalPaidAmount": 16000.00,        // 总实付金额（所有状态）
  "totalRefundedAmount": 1000.00,     // 总退款金额
  "totalDiscount": 3000.00,           // 总优惠金额
  "netRevenue": 14000.00,             // 净收入（有效收入-退款）
  "discountRate": "16.67",            // 优惠率（%）
  "mainOrder": {                      // 主订单数据
    "orderCount": 50,                 // 主订单总数（所有状态）
    "effectiveRevenue": 12000.00,     // 主订单有效收入
    "paidAmount": 13000.00,           // 主订单实付金额
    "refundedAmount": 800.00,         // 主订单退款金额
    "totalOriginalPrice": 15000.00,   // 主订单原价总额
    "effectiveOriginalPrice": 14000.00, // 有效主订单原价
    "totalDiscount": 2000.00,         // 主订单优惠金额
    "avgOrderValue": "240.00"         // 主订单平均价值
  },
  "additionalService": {              // 追加服务数据
    "orderCount": 20,                 // 追加服务订单总数
    "effectiveRevenue": 3000.00,      // 追加服务有效收入
    "paidAmount": 3200.00,            // 追加服务实付金额
    "refundedAmount": 200.00,         // 追加服务退款金额
    "totalOriginalPrice": 4000.00,    // 追加服务原价总额
    "effectiveOriginalPrice": 3800.00, // 有效追加服务原价
    "totalDiscount": 1000.00,         // 追加服务优惠金额
    "avgOrderValue": "150.00"         // 追加服务平均价值
  },
  "totalOrderCount": 70,              // 总订单数（所有状态）
  "effectiveOrderCount": 65,          // 有效订单数（已完成+已评价）
  "refundedOrderCount": 5,            // 退款订单数（退款中+已退款）
  "avgOrderValue": "214.29"           // 平均订单价值
}
```

### 关键指标说明

1. **有效收入 (totalRevenue)**
   - 已完成和已评价订单的收入总和
   - 代表确认的收入，不包括退款订单

2. **净收入 (netRevenue)**
   - 计算公式：有效收入 - 退款金额
   - 代表最终实际获得的收入

3. **优惠率 (discountRate)**
   - 计算公式：(总优惠金额 / 总原价) × 100%
   - 反映整体优惠力度

4. **平均订单价值 (avgOrderValue)**
   - 计算公式：有效收入 / 有效订单数量
   - 衡量单个有效订单的价值水平

5. **收入健康度分析**
   - 有效订单占比：effectiveOrderCount / totalOrderCount
   - 退款率：refundedOrderCount / totalOrderCount
   - 净收入率：netRevenue / totalPaidAmount

6. **收入构成分析**
   - 主订单收入占比
   - 追加服务收入占比
   - 帮助了解收入来源结构

### 趋势数据结构
```json
[
  {
    "period": "2024-01-01",           // 时间周期
    "mainOrder": {                    // 主订单数据
      "orderCount": 10,
      "totalRevenue": 2400.00,
      "totalOriginalPrice": 2800.00,
      "totalDiscount": 400.00
    },
    "additionalService": {            // 追加服务数据
      "orderCount": 5,
      "totalRevenue": 750.00,
      "totalOriginalPrice": 1000.00,
      "totalDiscount": 250.00
    },
    "totalRevenue": 3150.00,          // 当期总收入
    "totalOriginalPrice": 3800.00,    // 当期总原价
    "totalDiscount": 650.00,          // 当期总优惠
    "totalOrderCount": 15             // 当期总订单数
  }
]
```

## 注意事项

1. **性能考虑**：大时间范围的统计查询可能较慢，建议合理设置时间范围
2. **数据一致性**：统计数据基于订单的最终状态，已取消或退款的订单不计入统计
3. **时区处理**：所有时间统计基于服务器时区
4. **分页限制**：列表接口支持分页，建议设置合理的pageSize避免一次性加载过多数据

## 业务应用场景

### 1. 日常运营分析
- 查看每日/每周/每月的收入趋势
- 分析收入构成，了解主订单和追加服务的贡献比例
- 监控优惠活动对收入的影响

### 2. 服务项目分析
- 识别高收入服务项目
- 分析服务项目的订单量和平均价值
- 优化服务定价策略

### 3. 员工绩效评估
- 员工收入排行榜
- 员工业绩趋势分析
- 制定员工激励政策

### 4. 业务决策支持
- 基于历史数据预测未来收入
- 分析季节性收入变化
- 制定营销策略和促销计划
