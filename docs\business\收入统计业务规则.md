# 收入统计业务规则

## 概述
收入统计功能为管理后台提供全面的收入数据分析，支持多维度、多时间范围的收入统计和分析，帮助管理者了解业务收入状况、趋势变化和关键指标。

## 统计范围定义

### 订单状态范围
统计包含以下状态的订单：**已完成、已评价、退款中、已退款**，确保收入数据的完整性。

### 订单类型分类
1. **有效订单**: 状态为"已完成"和"已评价"的订单
   - 用于计算有效收入
   - 代表确认完成的业务收入

2. **退款订单**: 状态为"退款中"和"已退款"的订单
   - 用于计算退款金额
   - 影响净收入计算

## 收入计算规则

### 基础金额定义
1. **原价 (originalPrice)**
   - 订单的原始价格，未扣除任何优惠前的价格
   - 包括主订单和追加服务的原始定价

2. **实付金额 (paidAmount)**
   - 用户实际支付的金额（totalFee）
   - 包含所有状态订单的支付金额

3. **有效收入 (effectiveRevenue)**
   - 只计算已完成和已评价订单的收入
   - 代表确认的业务收入

4. **退款金额 (refundedAmount)**
   - 退款中和已退款订单的金额
   - 需要从总收入中扣除

5. **净收入 (netRevenue)**
   - 计算公式：有效收入 - 退款金额
   - 代表最终实际获得的收入

### 优惠计算规则
1. **优惠金额 (totalDiscount)**
   - 权益卡抵扣金额 + 代金券抵扣金额
   - 反映给客户的优惠总额

2. **优惠率 (discountRate)**
   - 计算公式：(优惠金额 / 原价) × 100%
   - 反映整体优惠力度

### 平均值计算规则
1. **平均订单价值 (avgOrderValue)**
   - 计算公式：有效收入 / 有效订单数量
   - 基于有效订单计算，排除退款订单影响

2. **员工平均收入**
   - 基于员工服务的有效订单计算
   - 包含主订单和追加服务收入

## 统计维度说明

### 1. 收入概览统计
- **用途**: 提供整体收入状况的快速概览
- **数据范围**: 全部或指定时间范围内的收入数据
- **关键指标**:
  - 总有效收入、净收入
  - 主订单与追加服务收入分布
  - 优惠率和平均订单价值
  - 订单数量分布（总数、有效数、退款数）

### 2. 收入趋势分析
- **用途**: 分析收入随时间的变化趋势
- **时间维度**: 支持按日、周、月统计
- **数据内容**: 每个时间周期的收入、订单量、优惠等数据
- **应用场景**: 识别收入增长趋势、季节性变化、异常波动

### 3. 服务维度统计
- **用途**: 分析不同服务项目的收入表现
- **筛选条件**: 支持按服务类型、具体服务筛选
- **关键指标**:
  - 服务订单量（总数、有效数、退款数）
  - 服务收入（原价、有效收入、退款金额、净收入）
  - 服务平均收入和基础价格
- **应用场景**: 识别高收入服务、优化服务定价、质量管控

### 4. 员工维度统计
- **用途**: 分析员工的收入贡献和服务表现
- **数据分类**: 分别统计主订单和追加服务数据
- **关键指标**:
  - 员工总收入、平均收入
  - 订单数量和服务质量
  - 退款率和客户满意度
- **应用场景**: 员工绩效评估、激励政策制定

### 5. 收入构成分析
- **用途**: 分析收入来源结构和优惠分布
- **分析维度**:
  - 主订单与追加服务收入占比
  - 优惠构成和优惠率分析
  - 订单数量构成分析
- **应用场景**: 业务结构优化、营销策略调整

## 关键业务指标

### 收入健康度指标
1. **有效订单占比**
   - 计算公式：有效订单数 / 总订单数
   - 反映订单完成质量

2. **退款率**
   - 计算公式：退款订单数 / 总订单数
   - 反映服务质量和客户满意度

3. **净收入率**
   - 计算公式：净收入 / 总实付金额
   - 反映最终收入效率

### 业务增长指标
1. **收入增长率**
   - 对比不同时间周期的收入变化
   - 识别业务增长趋势

2. **客单价变化**
   - 平均订单价值的时间变化
   - 反映客户消费水平变化

3. **服务渗透率**
   - 追加服务收入占比
   - 反映服务拓展能力

## 时间处理规则

### 时间范围设定
1. **默认范围**: 不传时间参数时统计全部历史数据
2. **指定范围**: 传入startDate和endDate时统计指定时间范围
3. **时间格式**: YYYY-MM-DD
4. **时间边界**: 结束时间包含当天23:59:59

### 趋势统计周期
1. **按日统计**: 每天一个数据点，适合短期分析
2. **按周统计**: 每周一个数据点，适合中期趋势分析
3. **按月统计**: 每月一个数据点，适合长期趋势分析

## 数据一致性保证

### 统计数据同步
1. **实时性**: 统计数据基于订单的当前状态
2. **一致性**: 所有统计维度使用相同的计算规则
3. **完整性**: 包含所有相关状态的订单数据

### 异常情况处理
1. **数据缺失**: 对于缺失的字段使用默认值0
2. **除零保护**: 在计算平均值和比率时进行除零检查
3. **精度控制**: 金额保留2位小数，比率保留2位小数

## 性能优化策略

### 查询优化
1. **索引使用**: 基于时间、状态、员工ID等字段建立索引
2. **分页查询**: 列表接口支持分页，避免大数据量查询
3. **缓存策略**: 对于频繁查询的统计数据考虑缓存

### 数据量控制
1. **时间范围限制**: 建议合理设置查询时间范围
2. **分页大小**: 建议pageSize不超过100
3. **并发控制**: 避免同时进行多个大范围统计查询

## 业务应用场景

### 1. 日常运营分析
- 查看每日/每周/每月的收入趋势
- 分析收入构成，了解主订单和追加服务的贡献比例
- 监控优惠活动对收入的影响
- 跟踪退款率和净收入变化趋势

### 2. 服务项目分析
- 识别高收入服务项目和退款率较高的服务
- 分析服务项目的订单量、有效收入和净收入
- 优化服务定价策略和质量管控

### 3. 员工绩效评估
- 员工收入排行榜和服务质量评估
- 员工业绩趋势分析和退款率监控
- 基于净收入和客户满意度制定员工激励政策

### 4. 业务决策支持
- 基于历史数据预测未来收入和退款趋势
- 分析季节性收入变化和客户行为模式
- 制定营销策略、促销计划和风险控制措施

## 注意事项

1. **性能考虑**: 大时间范围的统计查询可能较慢，建议合理设置时间范围
2. **数据完整性**: 统计包含已完成、已评价、退款中、已退款四种状态的订单，确保数据完整性
3. **收入区分**: 区分有效收入（已完成+已评价）和退款金额，提供净收入指标
4. **时区处理**: 所有时间统计基于服务器时区
5. **分页限制**: 列表接口支持分页，建议设置合理的pageSize避免一次性加载过多数据
