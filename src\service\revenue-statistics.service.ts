import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Op, fn, col, literal } from 'sequelize';
import { Order, OrderDetail, AdditionalServiceOrder, Service, ServiceType, Employee } from '../entity';
import { OrderStatus } from '../common/Constant';
import { AdditionalServiceOrderStatus } from '../entity/additional-service-order.entity';

@Provide()
export class RevenueStatisticsService {
  @Inject()
  ctx: Context;

  /**
   * 获取收入概览统计
   */
  async getRevenueOverview(startDate?: string, endDate?: string) {
    const whereCondition: any = {
      status: {
        [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
      },
    };

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
      };
    }

    // 主订单收入统计
    const mainOrderStats = await Order.findOne({
      where: whereCondition,
      attributes: [
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('originalPrice')), 'originalPrice'],
        [fn('SUM', col('totalFee')), 'totalFee'],
        [fn('SUM', col('cardDeduction')), 'cardDeduction'],
        [fn('SUM', col('couponDeduction')), 'couponDeduction'],
        [fn('AVG', col('totalFee')), 'avgOrderValue'],
      ],
      raw: true,
    }) as any;

    // 追加服务收入统计
    const additionalServiceStats = await AdditionalServiceOrder.findOne({
      where: {
        status: {
          [Op.in]: [AdditionalServiceOrderStatus.COMPLETED],
        },
        ...(startDate && endDate && {
          createdAt: {
            [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
          },
        }),
      },
      attributes: [
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('originalPrice')), 'originalPrice'],
        [fn('SUM', col('totalFee')), 'totalFee'],
        [fn('SUM', col('cardDeduction')), 'cardDeduction'],
        [fn('SUM', col('couponDeduction')), 'couponDeduction'],
        [fn('AVG', col('totalFee')), 'avgOrderValue'],
      ],
      raw: true,
    }) as any;

    // 计算总收入
    const mainRevenue = parseFloat(mainOrderStats?.totalFee || '0');
    const additionalRevenue = parseFloat(additionalServiceStats?.totalFee || '0');
    const totalRevenue = mainRevenue + additionalRevenue;

    // 计算总原价
    const mainOriginalPrice = parseFloat(mainOrderStats?.originalPrice || '0');
    const additionalOriginalPrice = parseFloat(additionalServiceStats?.originalPrice || '0');
    const totalOriginalPrice = mainOriginalPrice + additionalOriginalPrice;

    // 计算总优惠
    const mainCardDeduction = parseFloat(mainOrderStats?.cardDeduction || '0');
    const additionalCardDeduction = parseFloat(additionalServiceStats?.cardDeduction || '0');
    const mainCouponDeduction = parseFloat(mainOrderStats?.couponDeduction || '0');
    const additionalCouponDeduction = parseFloat(additionalServiceStats?.couponDeduction || '0');

    const totalDiscount = mainCardDeduction + additionalCardDeduction + mainCouponDeduction + additionalCouponDeduction;

    // 计算订单数量
    const mainOrderCount = parseInt(mainOrderStats?.orderCount || '0');
    const additionalOrderCount = parseInt(additionalServiceStats?.orderCount || '0');

    return {
      // 总体收入数据
      totalRevenue,
      totalOriginalPrice,
      totalDiscount,
      discountRate: totalOriginalPrice > 0 ? ((totalDiscount / totalOriginalPrice) * 100).toFixed(2) : '0.00',
      
      // 主订单数据
      mainOrder: {
        orderCount: mainOrderCount,
        totalRevenue: mainRevenue,
        totalOriginalPrice: mainOriginalPrice,
        totalDiscount: mainCardDeduction + mainCouponDeduction,
        avgOrderValue: parseFloat(mainOrderStats?.avgOrderValue || '0'),
      },

      // 追加服务数据
      additionalService: {
        orderCount: additionalOrderCount,
        totalRevenue: additionalRevenue,
        totalOriginalPrice: additionalOriginalPrice,
        totalDiscount: additionalCardDeduction + additionalCouponDeduction,
        avgOrderValue: parseFloat(additionalServiceStats?.avgOrderValue || '0'),
      },
      
      // 总订单数据
      totalOrderCount: mainOrderCount + additionalOrderCount,
      avgOrderValue: (mainOrderCount + additionalOrderCount) > 0 
        ? (totalRevenue / (mainOrderCount + additionalOrderCount)).toFixed(2) 
        : '0.00',
    };
  }

  /**
   * 获取收入趋势统计
   */
  async getRevenueTrend(startDate: string, endDate: string, periodType: 'day' | 'week' | 'month' = 'day') {
    let dateFormat: string;
    let groupBy: string;

    switch (periodType) {
      case 'week':
        dateFormat = '%Y-%u';
        groupBy = 'YEARWEEK(createdAt)';
        break;
      case 'month':
        dateFormat = '%Y-%m';
        groupBy = 'DATE_FORMAT(createdAt, "%Y-%m")';
        break;
      default:
        dateFormat = '%Y-%m-%d';
        groupBy = 'DATE(createdAt)';
    }

    const whereCondition = {
      status: {
        [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
      },
      createdAt: {
        [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
      },
    };

    // 主订单趋势
    const mainOrderTrend = await Order.findAll({
      where: whereCondition,
      attributes: [
        [literal(`DATE_FORMAT(createdAt, '${dateFormat}')`), 'period'],
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalRevenue'],
        [fn('SUM', col('originalPrice')), 'totalOriginalPrice'],
        [fn('SUM', col('cardDeduction')), 'totalCardDeduction'],
        [fn('SUM', col('couponDeduction')), 'totalCouponDeduction'],
      ],
      group: [literal(`DATE_FORMAT(createdAt, '${dateFormat}')`)],
      order: [[literal('period'), 'ASC']],
      raw: true,
    });

    // 追加服务趋势
    const additionalServiceTrend = await AdditionalServiceOrder.findAll({
      where: {
        status: {
          [Op.in]: [AdditionalServiceOrderStatus.COMPLETED],
        },
        createdAt: {
          [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
        },
      },
      attributes: [
        [literal(`DATE_FORMAT(createdAt, '${dateFormat}')`), 'period'],
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalRevenue'],
        [fn('SUM', col('originalPrice')), 'totalOriginalPrice'],
        [fn('SUM', col('cardDeduction')), 'totalCardDeduction'],
        [fn('SUM', col('couponDeduction')), 'totalCouponDeduction'],
      ],
      group: [literal(`DATE_FORMAT(createdAt, '${dateFormat}')`)],
      order: [[literal('period'), 'ASC']],
      raw: true,
    });

    // 合并数据
    const trendMap = new Map();

    // 处理主订单数据
    mainOrderTrend.forEach((item: any) => {
      const period = item.period;
      trendMap.set(period, {
        period,
        mainOrder: {
          orderCount: parseInt(item.orderCount),
          totalRevenue: parseFloat(item.totalRevenue || '0'),
          totalOriginalPrice: parseFloat(item.totalOriginalPrice || '0'),
          totalDiscount: parseFloat(item.totalCardDeduction || '0') + parseFloat(item.totalCouponDeduction || '0'),
        },
        additionalService: {
          orderCount: 0,
          totalRevenue: 0,
          totalOriginalPrice: 0,
          totalDiscount: 0,
        },
      });
    });

    // 处理追加服务数据
    additionalServiceTrend.forEach((item: any) => {
      const period = item.period;
      const existing = trendMap.get(period) || {
        period,
        mainOrder: {
          orderCount: 0,
          totalRevenue: 0,
          totalOriginalPrice: 0,
          totalDiscount: 0,
        },
        additionalService: {
          orderCount: 0,
          totalRevenue: 0,
          totalOriginalPrice: 0,
          totalDiscount: 0,
        },
      };

      existing.additionalService = {
        orderCount: parseInt(item.orderCount),
        totalRevenue: parseFloat(item.totalRevenue || '0'),
        totalOriginalPrice: parseFloat(item.totalOriginalPrice || '0'),
        totalDiscount: parseFloat(item.totalCardDeduction || '0') + parseFloat(item.totalCouponDeduction || '0'),
      };

      trendMap.set(period, existing);
    });

    // 转换为数组并计算总计
    const trendData = Array.from(trendMap.values()).map((item: any) => ({
      ...item,
      totalRevenue: item.mainOrder.totalRevenue + item.additionalService.totalRevenue,
      totalOriginalPrice: item.mainOrder.totalOriginalPrice + item.additionalService.totalOriginalPrice,
      totalDiscount: item.mainOrder.totalDiscount + item.additionalService.totalDiscount,
      totalOrderCount: item.mainOrder.orderCount + item.additionalService.orderCount,
    }));

    return trendData;
  }

  /**
   * 获取服务收入统计
   */
  async getServiceRevenueStatistics(
    startDate?: string,
    endDate?: string,
    serviceTypeId?: number,
    serviceId?: number,
    page = 1,
    pageSize = 20,
    sortBy: 'totalRevenue' | 'orderCount' | 'avgRevenue' = 'totalRevenue',
    sortOrder: 'asc' | 'desc' = 'desc'
  ) {
    const whereCondition: any = {
      status: {
        [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
      },
    };

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
      };
    }

    // 构建服务查询条件
    const serviceWhere: any = {};
    if (serviceTypeId) {
      serviceWhere.serviceTypeId = serviceTypeId;
    }
    if (serviceId) {
      serviceWhere.id = serviceId;
    }

    // 主订单服务统计
    const serviceStats = await OrderDetail.findAll({
      where: {},
      include: [
        {
          model: Order,
          where: whereCondition,
          attributes: [],
        },
        {
          model: Service,
          where: serviceWhere,
          include: [
            {
              model: ServiceType,
              attributes: ['id', 'name', 'type'],
            },
          ],
        },
      ],
      attributes: [
        'serviceId',
        'serviceName',
        'servicePrice',
        [fn('COUNT', col('OrderDetail.id')), 'orderCount'],
        [fn('SUM', col('servicePrice')), 'totalRevenue'],
        [fn('AVG', col('servicePrice')), 'avgRevenue'],
      ],
      group: ['serviceId', 'serviceName', 'servicePrice', 'service.id', 'service.serviceType.id'],
      order: [[fn('SUM', col('servicePrice')), sortOrder.toUpperCase()]],
      offset: (page - 1) * pageSize,
      limit: pageSize,
      subQuery: false,
    });

    // 获取总数
    const totalCount = await OrderDetail.findAll({
      where: {},
      include: [
        {
          model: Order,
          where: whereCondition,
          attributes: [],
        },
        {
          model: Service,
          where: serviceWhere,
          attributes: [],
        },
      ],
      attributes: ['serviceId'],
      group: ['serviceId'],
      raw: true,
    });

    const formattedStats = serviceStats.map((item: any) => ({
      serviceId: item.serviceId,
      serviceName: item.serviceName,
      serviceType: item.service?.serviceType?.name || '未知类型',
      serviceTypeId: item.service?.serviceType?.id,
      orderCount: parseInt(item.orderCount),
      totalRevenue: parseFloat(item.totalRevenue || '0'),
      avgRevenue: parseFloat(item.avgRevenue || '0'),
      basePrice: parseFloat(item.servicePrice || '0'),
    }));

    return {
      list: formattedStats,
      total: totalCount.length,
      page,
      pageSize,
    };
  }

  /**
   * 获取员工收入统计
   */
  async getEmployeeRevenueStatistics(
    startDate?: string,
    endDate?: string,
    employeeId?: number,
    page = 1,
    pageSize = 20,
    sortBy: 'totalRevenue' | 'orderCount' | 'avgRevenue' = 'totalRevenue',
    sortOrder: 'asc' | 'desc' = 'desc'
  ) {
    const whereCondition: any = {
      status: {
        [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
      },
    };

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
      };
    }

    if (employeeId) {
      whereCondition.employeeId = employeeId;
    }

    // 主订单员工统计
    const employeeStats = await Order.findAll({
      where: whereCondition,
      attributes: [
        'employeeId',
        [fn('COUNT', col('Order.id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalRevenue'],
        [fn('SUM', col('originalPrice')), 'totalOriginalPrice'],
        [fn('SUM', col('cardDeduction')), 'totalCardDeduction'],
        [fn('SUM', col('couponDeduction')), 'totalCouponDeduction'],
        [fn('AVG', col('totalFee')), 'avgRevenue'],
      ],
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar', 'rating'],
          required: true,
        },
      ],
      group: ['employeeId', 'employee.id'],
      order: [[fn('SUM', col('totalFee')), sortOrder.toUpperCase()]],
      offset: (page - 1) * pageSize,
      limit: pageSize,
      subQuery: false,
    });

    // 获取追加服务收入
    const additionalServiceRevenue = await AdditionalServiceOrder.findAll({
      where: {
        status: {
          [Op.in]: [AdditionalServiceOrderStatus.COMPLETED],
        },
        ...(startDate && endDate && {
          createdAt: {
            [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
          },
        }),
        ...(employeeId && { employeeId }),
      },
      attributes: [
        'employeeId',
        [fn('COUNT', col('id')), 'additionalOrderCount'],
        [fn('SUM', col('totalFee')), 'additionalRevenue'],
        [fn('SUM', col('originalPrice')), 'additionalOriginalPrice'],
        [fn('SUM', col('cardDeduction')), 'additionalCardDeduction'],
        [fn('SUM', col('couponDeduction')), 'additionalCouponDeduction'],
      ],
      group: ['employeeId'],
      raw: true,
    });

    // 合并数据
    const additionalMap = new Map();
    additionalServiceRevenue.forEach((item: any) => {
      additionalMap.set(item.employeeId, item);
    });

    const formattedStats = employeeStats.map((item: any) => {
      const additional = additionalMap.get(item.employeeId) || {};
      const mainRevenue = parseFloat(item.totalRevenue || '0');
      const additionalRev = parseFloat(additional.additionalRevenue || '0');
      const totalRevenue = mainRevenue + additionalRev;

      return {
        employeeId: item.employeeId,
        employee: item.employee,
        mainOrder: {
          orderCount: parseInt(item.orderCount),
          totalRevenue: mainRevenue,
          totalOriginalPrice: parseFloat(item.totalOriginalPrice || '0'),
          totalDiscount: parseFloat(item.totalCardDeduction || '0') + parseFloat(item.totalCouponDeduction || '0'),
          avgRevenue: parseFloat(item.avgRevenue || '0'),
        },
        additionalService: {
          orderCount: parseInt(additional.additionalOrderCount || '0'),
          totalRevenue: additionalRev,
          totalOriginalPrice: parseFloat(additional.additionalOriginalPrice || '0'),
          totalDiscount: parseFloat(additional.additionalCardDeduction || '0') + parseFloat(additional.additionalCouponDeduction || '0'),
        },
        totalRevenue,
        totalOrderCount: parseInt(item.orderCount) + parseInt(additional.additionalOrderCount || '0'),
        avgRevenue: totalRevenue > 0 && (parseInt(item.orderCount) + parseInt(additional.additionalOrderCount || '0')) > 0
          ? (totalRevenue / (parseInt(item.orderCount) + parseInt(additional.additionalOrderCount || '0'))).toFixed(2)
          : '0.00',
      };
    });

    // 获取总数
    const totalCount = await Order.findAll({
      where: whereCondition,
      attributes: ['employeeId'],
      group: ['employeeId'],
      raw: true,
    });

    return {
      list: formattedStats,
      total: totalCount.length,
      page,
      pageSize,
    };
  }
}
