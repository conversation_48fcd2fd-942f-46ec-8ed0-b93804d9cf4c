import { Controller, Get, Query, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { RevenueStatisticsService } from '../service/revenue-statistics.service';
import {
  RevenueStatisticsQueryDTO,
  RevenueTrendQueryDTO,
  ServiceRevenueQueryDTO,
  EmployeeRevenueQueryDTO,
} from '../dto/revenue-statistics.dto';

@Controller('/revenue-statistics')
export class RevenueStatisticsController {
  @Inject()
  ctx: Context;

  @Inject()
  revenueStatisticsService: RevenueStatisticsService;

  /**
   * 获取收入概览统计
   * GET /revenue-statistics/overview
   */
  @Get('/overview', { summary: '获取收入概览统计' })
  async getOverview(@Query() query: RevenueStatisticsQueryDTO) {
    const { startDate, endDate } = query;
    return await this.revenueStatisticsService.getRevenueOverview(startDate, endDate);
  }

  /**
   * 获取收入趋势统计
   * GET /revenue-statistics/trend
   */
  @Get('/trend', { summary: '获取收入趋势统计' })
  async getTrend(@Query() query: RevenueTrendQueryDTO) {
    const { startDate, endDate, periodType = 'day' } = query;

    if (!startDate || !endDate) {
      throw new Error('开始日期和结束日期不能为空');
    }

    return await this.revenueStatisticsService.getRevenueTrend(startDate, endDate, periodType);
  }

  /**
   * 获取服务收入统计
   * GET /revenue-statistics/service
   */
  @Get('/service', { summary: '获取服务收入统计' })
  async getServiceStatistics(@Query() query: ServiceRevenueQueryDTO) {
    const {
      startDate,
      endDate,
      serviceTypeId,
      serviceId,
      page = 1,
      pageSize = 20,
      sortBy = 'totalRevenue',
      sortOrder = 'desc',
    } = query;

    return await this.revenueStatisticsService.getServiceRevenueStatistics(
      startDate,
      endDate,
      serviceTypeId,
      serviceId,
      page,
      pageSize,
      sortBy,
      sortOrder
    );
  }

  /**
   * 获取员工收入统计
   * GET /revenue-statistics/employee
   */
  @Get('/employee', { summary: '获取员工收入统计' })
  async getEmployeeStatistics(@Query() query: EmployeeRevenueQueryDTO) {
    const {
      startDate,
      endDate,
      employeeId,
      page = 1,
      pageSize = 20,
      sortBy = 'totalRevenue',
      sortOrder = 'desc',
    } = query;

    return await this.revenueStatisticsService.getEmployeeRevenueStatistics(
      startDate,
      endDate,
      employeeId,
      page,
      pageSize,
      sortBy,
      sortOrder
    );
  }

  /**
   * 获取收入构成分析
   * GET /revenue-statistics/composition
   */
  @Get('/composition', { summary: '获取收入构成分析' })
  async getRevenueComposition(@Query() query: RevenueStatisticsQueryDTO) {
    const { startDate, endDate } = query;
    const overview = await this.revenueStatisticsService.getRevenueOverview(startDate, endDate);
    
    return {
      // 主订单与追加服务收入占比
      revenueComposition: {
        mainOrderRevenue: overview.mainOrder.totalRevenue,
        mainOrderPercentage: overview.totalRevenue > 0 
          ? ((overview.mainOrder.totalRevenue / overview.totalRevenue) * 100).toFixed(2)
          : '0.00',
        additionalServiceRevenue: overview.additionalService.totalRevenue,
        additionalServicePercentage: overview.totalRevenue > 0 
          ? ((overview.additionalService.totalRevenue / overview.totalRevenue) * 100).toFixed(2)
          : '0.00',
      },
      
      // 优惠构成分析
      discountComposition: {
        totalDiscount: overview.totalDiscount,
        discountRate: overview.discountRate,
        mainOrderDiscount: overview.mainOrder.totalDiscount,
        additionalServiceDiscount: overview.additionalService.totalDiscount,
      },
      
      // 订单数量构成
      orderComposition: {
        totalOrderCount: overview.totalOrderCount,
        mainOrderCount: overview.mainOrder.orderCount,
        mainOrderPercentage: overview.totalOrderCount > 0 
          ? ((overview.mainOrder.orderCount / overview.totalOrderCount) * 100).toFixed(2)
          : '0.00',
        additionalServiceCount: overview.additionalService.orderCount,
        additionalServicePercentage: overview.totalOrderCount > 0 
          ? ((overview.additionalService.orderCount / overview.totalOrderCount) * 100).toFixed(2)
          : '0.00',
      },
    };
  }
}
